package cn.com.chinastock.cnf.kafka.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Galaxy Kafka 特有配置属性
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "galaxy.kafka")
public class GalaxyKafkaProperties {

    public static final int DEFAULT_MAX_DETAIL_RECORDS_COUNT = 5;

    /**
     * 是否启用Galaxy Kafka功能
     */
    private boolean enabled = false;

    /**
     * 日志拦截器配置
     */
    private Log log = new Log();

    /**
     * 链路追踪配置
     */
    private Tracing tracing = new Tracing();

    /**
     * 获取是否启用Galaxy Kafka功能
     *
     * @return 是否启用
     */
    public boolean isEnabled() {
        return enabled;
    }

    /**
     * 设置是否启用Galaxy Kafka功能
     *
     * @param enabled 是否启用
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    /**
     * 获取日志配置
     *
     * @return 日志配置
     */
    public Log getLog() {
        return log;
    }

    /**
     * 设置日志配置
     *
     * @param log 日志配置
     */
    public void setLog(Log log) {
        this.log = log;
    }

    /**
     * 获取链路追踪配置
     *
     * @return 链路追踪配置
     */
    public Tracing getTracing() {
        return tracing;
    }

    /**
     * 设置链路追踪配置
     *
     * @param tracing 链路追踪配置
     */
    public void setTracing(Tracing tracing) {
        this.tracing = tracing;
    }

    /**
     * 获取是否启用日志拦截器（兼容性方法）
     *
     * @return 是否启用日志拦截器
     */
    public boolean isLogEnabled() {
        return log.isEnabled();
    }

    /**
     * 设置是否启用日志拦截器（兼容性方法）
     *
     * @param logEnabled 是否启用日志拦截器
     */
    public void setLogEnabled(boolean logEnabled) {
        this.log.setEnabled(logEnabled);
    }

    /**
     * 获取最大批量详细记录数（兼容性方法）
     *
     * @return 最大批量详细记录数
     */
    public int getMaxBatchDetailCount() {
        return log.getMaxBatchDetailCount();
    }

    /**
     * 设置最大批量详细记录数（兼容性方法）
     *
     * @param maxBatchDetailCount 最大批量详细记录数
     */
    public void setMaxBatchDetailCount(int maxBatchDetailCount) {
        this.log.setMaxBatchDetailCount(maxBatchDetailCount);
    }

    /**
     * 日志拦截器配置类
     */
    public static class Log {
        /**
         * 是否启用 Kafka 日志拦截器
         */
        private boolean enabled = true;

        /**
         * Batch消费场景下，消费者详细日志记录的最大记录数
         */
        private int maxBatchDetailCount = DEFAULT_MAX_DETAIL_RECORDS_COUNT;

        /**
         * 获取是否启用日志拦截器
         *
         * @return 是否启用日志拦截器
         */
        public boolean isEnabled() {
            return enabled;
        }

        /**
         * 设置是否启用日志拦截器
         *
         * @param enabled 是否启用日志拦截器
         */
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        /**
         * 获取最大批量详细记录数
         *
         * @return 最大批量详细记录数
         */
        public int getMaxBatchDetailCount() {
            return maxBatchDetailCount;
        }

        /**
         * 设置最大批量详细记录数
         *
         * @param maxBatchDetailCount 最大批量详细记录数
         */
        public void setMaxBatchDetailCount(int maxBatchDetailCount) {
            this.maxBatchDetailCount = maxBatchDetailCount;
        }
    }

    /**
     * 链路追踪配置类
     */
    public static class Tracing {
        /**
         * 是否启用批量消息处理的统一链路追踪
         */
        private boolean unifiedBatchEnabled = true;

        /**
         * 获取是否启用批量消息处理的统一链路追踪
         *
         * @return 是否启用统一链路追踪
         */
        public boolean isUnifiedBatchEnabled() {
            return unifiedBatchEnabled;
        }

        /**
         * 设置是否启用批量消息处理的统一链路追踪
         *
         * @param unifiedBatchEnabled 是否启用统一链路追踪
         */
        public void setUnifiedBatchEnabled(boolean unifiedBatchEnabled) {
            this.unifiedBatchEnabled = unifiedBatchEnabled;
        }
    }
}
