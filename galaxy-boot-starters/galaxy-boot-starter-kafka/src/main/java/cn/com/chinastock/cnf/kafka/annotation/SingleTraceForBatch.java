package cn.com.chinastock.cnf.kafka.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 批量消息处理统一链路追踪注解
 * 
 * <p>该注解用于标记需要在统一链路追踪上下文中执行的批量消息处理方法。
 * 当方法被此注解标记时，整个方法的执行过程将在同一个Span下进行，
 * 确保所有相关的操作（包括发送消息到其他topic）都使用相同的traceId。</p>
 * 
 * <p>支持的方法类型：</p>
 * <ul>
 *     <li>同步方法（返回void或其他非响应式类型）</li>
 *     <li>响应式方法（返回Mono或Flux）</li>
 * </ul>
 * 
 * <p>使用示例：</p>
 * <pre>{@code
 * @KafkaListener(topics = "batch-topic", groupId = "batch-group")
 * @SingleTraceForBatch(spanName = "batch-message-processing")
 * public void processBatchMessages(List<ConsumerRecord<String, String>> records) {
 *     // 批量处理逻辑
 *     // 发送消息到其他topic的操作也会在同一个trace下
 * }
 * }</pre>
 * 
 * <AUTHOR> Boot Team
 * @since 0.5.4
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SingleTraceForBatch {
    
    /**
     * Span名称，用于标识这个批量处理操作
     * 
     * @return Span名称，默认为"batch-processing"
     */
    String spanName() default "batch-processing";
    
    /**
     * 是否记录批量大小到Span标签中
     * 
     * @return 是否记录批量大小，默认为true
     */
    boolean recordBatchSize() default true;
    
    /**
     * 自定义标签，格式为"key=value"
     * 
     * @return 自定义标签数组
     */
    String[] tags() default {};
}
