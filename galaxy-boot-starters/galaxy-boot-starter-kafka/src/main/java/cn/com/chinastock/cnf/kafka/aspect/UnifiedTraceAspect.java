package cn.com.chinastock.cnf.kafka.aspect;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.kafka.annotation.SingleTraceForBatch;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import io.micrometer.tracing.otel.bridge.OtelTraceContextBuilder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import static cn.com.chinastock.cnf.core.log.context.TraceConstants.*;

/**
 * 批量消息处理统一链路追踪切面
 *
 * <p>该切面用于处理标记了{@link SingleTraceForBatch}注解的方法，
 * 确保整个批量处理过程在统一的链路追踪上下文中执行。</p>
 *
 * <p>主要功能：</p>
 * <ul>
 *     <li>为批量处理方法创建统一的Span</li>
 *     <li>支持同步和响应式（Mono/Flux）方法</li>
 *     <li>自动记录批量大小和自定义标签</li>
 *     <li>确保所有子操作（如发送消息）使用相同的traceId</li>
 * </ul>
 *
 * <AUTHOR> Boot Team
 * @since 0.5.4
 */
@Aspect
@Component
public class UnifiedTraceAspect {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(UnifiedTraceAspect.class);

    // 检查Reactor是否在classpath中
    private static final boolean REACTOR_PRESENT = ClassUtils.isPresent("reactor.core.publisher.Mono", UnifiedTraceAspect.class.getClassLoader());

    @Autowired(required = false)
    private Tracer tracer;

    /**
     * 拦截标记了@SingleTraceForBatch注解的方法
     *
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 方法执行过程中可能抛出的异常
     */
    @Around("@annotation(cn.com.chinastock.cnf.kafka.annotation.SingleTraceForBatch)")
    public Object traceBatchProcessing(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        logger.info(LogCategory.FRAMEWORK_LOG, "UnifiedTraceAspect: Intercepting method {} with @SingleTraceForBatch",
                signature.getMethod().getName());

        // 检查Tracer是否可用
        if (tracer == null) {
            logger.warn(LogCategory.FRAMEWORK_LOG, "UnifiedTraceAspect: Tracer not available, executing method without tracing enhancement");
            return joinPoint.proceed();
        }

        // 判断返回类型，决定处理策略
        Class<?> returnType = signature.getReturnType();
        if (REACTOR_PRESENT && isReactiveType(returnType)) {
            // 响应式路径 (Reactive Path)
            return handleReactive(joinPoint);
        } else {
            // 同步路径 (Synchronous Path)
            return handleSync(joinPoint);
        }
    }

    private Object handleSync(ProceedingJoinPoint joinPoint) throws Throwable {
        Span span = createSpan();
        logger.debug(LogCategory.FRAMEWORK_LOG, "[Sync] Created a new span for batch processing with TraceId: {}", span.context().traceId());

        try (Tracer.SpanInScope ws = tracer.withSpan(span)) {
            // 设置标签
            return joinPoint.proceed();
        } catch (Throwable throwable) {
            span.error(throwable);
            throw throwable;
        } finally {
            span.end();
            logger.debug(LogCategory.FRAMEWORK_LOG, "[Sync] Ended span for batch processing");
        }
    }

    private Span createSpan() {
        Span span = tracer.currentSpan();
        if (span == null) {
            String traceId = MDC.get(TRACE_ID);
            String spanId = MDC.get(SPAN_ID);
            String parentSpanId = MDC.get(PARENT_SPAN_ID);
            span = tracer.spanBuilder().setParent(new OtelTraceContextBuilder().traceId(traceId).spanId(spanId).parentId(parentSpanId).sampled(false).build()).start();
        }
        return span;
    }

    private Object handleReactive(ProceedingJoinPoint joinPoint) {
        logger.debug(LogCategory.FRAMEWORK_LOG, "[Reactive] Processing reactive method with unified tracing");

        try {
            // 执行原始方法获取响应式结果
            Object result = joinPoint.proceed();

            if (result instanceof Mono) {
                return handleReactiveMono((Mono<?>) result);
            } else if (result instanceof Flux) {
                return handleReactiveFlux((Flux<?>) result);
            } else {
                // 不应该到达这里，但为了安全起见
                logger.warn(LogCategory.FRAMEWORK_LOG, "[Reactive] Unexpected return type: {}, falling back to sync processing",
                    result != null ? result.getClass().getName() : "null");
                return result;
            }
        } catch (Throwable e) {
            logger.error(LogCategory.FRAMEWORK_LOG, "[Reactive] Error executing reactive method", e);
            throw new RuntimeException(e);
        }
    }

    private Mono<?> handleReactiveMono(Mono<?> mono) {
        return mono
            .transformDeferredContextual((originalMono, contextView) -> {
                // 创建新的Span
                Span span = createSpan();
                logger.debug(LogCategory.FRAMEWORK_LOG, "[Reactive-Mono] Created span with TraceId: {}", span.context().traceId());

                return originalMono
                    .doOnSubscribe(subscription -> {
                        logger.debug(LogCategory.FRAMEWORK_LOG, "[Reactive-Mono] Subscribed to stream");
                    })
                    .doOnNext(value -> {
                        logger.debug(LogCategory.FRAMEWORK_LOG, "[Reactive-Mono] Processing value in span: {}", span.context().traceId());
                    })
                    .doOnError(throwable -> {
                        logger.error(LogCategory.FRAMEWORK_LOG, "[Reactive-Mono] Error in span: {}", span.context().traceId(), throwable);
                        span.error(throwable);
                    })
                    .doFinally(signalType -> {
                        logger.debug(LogCategory.FRAMEWORK_LOG, "[Reactive-Mono] Ending span with signal: {}", signalType);
                        span.end();
                    })
                    // 将Span放入Reactor Context，确保下游操作可以访问
                    .contextWrite(context -> context.put(Span.class, span));
            });
    }

    private Flux<?> handleReactiveFlux(Flux<?> flux) {
        return flux
            .transformDeferredContextual((originalFlux, contextView) -> {
                // 创建新的Span
                Span span = createSpan();
                logger.debug(LogCategory.FRAMEWORK_LOG, "[Reactive-Flux] Created span with TraceId: {}", span.context().traceId());

                return originalFlux
                    .doOnSubscribe(subscription -> {
                        logger.debug(LogCategory.FRAMEWORK_LOG, "[Reactive-Flux] Subscribed to stream");
                    })
                    .doOnNext(value -> {
                        logger.debug(LogCategory.FRAMEWORK_LOG, "[Reactive-Flux] Processing value in span: {}", span.context().traceId());
                    })
                    .doOnError(throwable -> {
                        logger.error(LogCategory.FRAMEWORK_LOG, "[Reactive-Flux] Error in span: {}", span.context().traceId(), throwable);
                        span.error(throwable);
                    })
                    .doFinally(signalType -> {
                        logger.debug(LogCategory.FRAMEWORK_LOG, "[Reactive-Flux] Ending span with signal: {}", signalType);
                        span.end();
                    })
                    // 将Span放入Reactor Context，确保下游操作可以访问
                    .contextWrite(context -> context.put(Span.class, span));
            });
    }

    private boolean isReactiveType(Class<?> returnType) {
        try {
            Class<?> monoClass = Class.forName("reactor.core.publisher.Mono");
            Class<?> fluxClass = Class.forName("reactor.core.publisher.Flux");
            return monoClass.isAssignableFrom(returnType) || fluxClass.isAssignableFrom(returnType);
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
}
