# Galaxy Boot Kafka 统一链路追踪

## 概述

Galaxy Boot Kafka Starter 提供了统一链路追踪功能，用于解决Kafka批量消息处理中traceId不一致的问题。通过使用 `@SingleTraceForBatch` 注解，可以确保整个批量处理过程（包括发送消息到其他topic的操作）都在同一个Span下执行，使用相同的traceId。

## 功能特性

- **统一链路追踪**：确保批量处理方法中的所有操作使用相同的traceId
- **同步和响应式支持**：支持同步方法和响应式方法（Mono/Flux）
- **自动批量大小记录**：自动记录批量消息的数量到Span标签中
- **自定义标签支持**：支持添加自定义标签到Span中
- **条件启用**：可通过配置控制是否启用该功能

## 使用方法

### 1. 启用功能

在 `application.yml` 中配置：

```yaml
galaxy:
  kafka:
    enabled: true
    tracing:
      unified-batch-enabled: true  # 默认为true
```

### 2. 添加注解

在需要统一链路追踪的批量消息处理方法上添加 `@SingleTraceForBatch` 注解：

```java
@Component
public class BatchMessageConsumer {

    @KafkaListener(topics = "batch-topic", groupId = "batch-group")
    @SingleTraceForBatch(spanName = "batch-message-processing")
    public void processBatchMessages(List<ConsumerRecord<String, String>> records) {
        // 批量处理逻辑
        for (ConsumerRecord<String, String> record : records) {
            processMessage(record);
        }
        
        // 发送消息到其他topic - 这些操作也会在同一个trace下
        kafkaProducerService.sendMessage("another-topic", "processed-data");
    }
}
```

### 3. 注解参数

`@SingleTraceForBatch` 注解支持以下参数：

- `spanName`：Span名称，默认为 "batch-processing"
- `recordBatchSize`：是否记录批量大小到Span标签，默认为 true
- `tags`：自定义标签数组，格式为 "key=value"

示例：

```java
@SingleTraceForBatch(
    spanName = "batch-metadata-processing",
    recordBatchSize = true,
    tags = {"consumer.type=batch-metadata", "topic=batch-topic"}
)
public void processBatchMessagesWithMetadata(List<ConsumerRecord<String, String>> records) {
    // 处理逻辑
}
```

## 支持的方法类型

### 同步方法

```java
@SingleTraceForBatch(spanName = "sync-batch-processing")
public void processBatchSync(List<ConsumerRecord<String, String>> records) {
    // 同步处理逻辑
}
```

### 响应式方法

```java
@SingleTraceForBatch(spanName = "reactive-batch-processing")
public Mono<Void> processBatchReactive(List<ConsumerRecord<String, String>> records) {
    return Mono.fromRunnable(() -> {
        // 响应式处理逻辑
    });
}

@SingleTraceForBatch(spanName = "flux-batch-processing")
public Flux<String> processBatchFlux(List<ConsumerRecord<String, String>> records) {
    return Flux.fromIterable(records)
        .map(record -> processRecord(record));
}
```

## 工作原理

1. **AOP拦截**：`UnifiedTraceAspect` 切面拦截标记了 `@SingleTraceForBatch` 注解的方法
2. **Span创建**：为批量处理方法创建一个新的Span
3. **上下文传播**：确保所有子操作（如发送消息）都在这个Span的上下文中执行
4. **标签设置**：自动设置批量大小和自定义标签
5. **生命周期管理**：正确管理Span的生命周期，包括异常处理

## 配置选项

```yaml
galaxy:
  kafka:
    enabled: true
    tracing:
      unified-batch-enabled: true  # 是否启用统一链路追踪，默认true
```

## 日志示例

启用统一链路追踪后，日志中的traceId将保持一致：

```
# 处理批量消息
V1|2025-09-04T21:18:54.993+0800|...|6fc5a4a46e3b4ba912439abb07f32d69|7fac7cf3bdd04344|d03a4f3e9def4e1b|...|Batch consumer with metadata received 1 records

# 发送消息到其他topic - 使用相同的traceId
V1|2025-09-04T21:18:55.005+0800|...|6fc5a4a46e3b4ba912439abb07f32d69|7fac7cf3bdd04344|cfa3c40d29c5c464|...|Sending message to topic: another-topic
```

## 注意事项

1. **方法参数要求**：被注解的方法必须包含一个 `List` 类型的参数，用于确定批量大小
2. **Micrometer依赖**：需要项目中包含 Micrometer Tracing 相关依赖
3. **AOP代理**：确保Spring AOP功能正常工作，被注解的方法不能是private或final
4. **性能考虑**：每个批量处理都会创建一个新的Span，在高并发场景下需要考虑性能影响

## 故障排除

### 功能未生效

1. 检查配置是否正确启用
2. 确认Micrometer Tracing依赖是否存在
3. 验证方法是否被Spring管理（@Component等注解）
4. 确认方法参数包含List类型

### 日志中看不到统一的traceId

1. 检查日志配置是否正确
2. 确认Tracer Bean是否正确配置
3. 验证方法调用是否通过Spring代理

## 示例项目

参考 `galaxy-boot-kafka-example` 项目中的 `BatchMessageConsumer` 类，了解完整的使用示例。
