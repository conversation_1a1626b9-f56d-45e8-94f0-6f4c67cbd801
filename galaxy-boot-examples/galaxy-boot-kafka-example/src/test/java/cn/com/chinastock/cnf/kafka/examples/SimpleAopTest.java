package cn.com.chinastock.cnf.kafka.examples;

import cn.com.chinastock.cnf.kafka.examples.consumer.BatchMessageConsumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.TestPropertySource;
import cn.com.chinastock.cnf.kafka.examples.service.KafkaProducerService;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 简单的AOP功能测试
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@TestPropertySource(properties = {
    "galaxy.kafka.enabled=true",
    "galaxy.kafka.tracing.unified-batch-enabled=true",
    "spring.kafka.bootstrap-servers=localhost:9092",
    "spring.kafka.consumer.group-id=test-group"
})
class SimpleAopTest {

    @Autowired
    private BatchMessageConsumer batchMessageConsumer;

    @Autowired
    private ApplicationContext applicationContext;

    @MockBean
    private KafkaProducerService kafkaProducerService;

    @Test
    void testAopFunctionality() {
        // 验证Bean正确注入
        assertNotNull(batchMessageConsumer);
        assertNotNull(applicationContext);

        // 检查所有Bean
        System.out.println("=== 所有Bean列表 ===");
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        for (String beanName : beanNames) {
            if (beanName.toLowerCase().contains("aspect") || beanName.toLowerCase().contains("trace") ||
                beanName.toLowerCase().contains("kafka") || beanName.toLowerCase().contains("galaxy")) {
                System.out.println("Found Bean: " + beanName + " -> " + applicationContext.getBean(beanName).getClass().getName());
            }
        }

        // 检查UnifiedTraceAspect是否被创建
        try {
            Object aspect = applicationContext.getBean("unifiedTraceAspect");
            System.out.println("✅ UnifiedTraceAspect Bean找到：" + aspect.getClass().getName());
        } catch (Exception e) {
            System.out.println("❌ UnifiedTraceAspect Bean未找到：" + e.getMessage());
        }

        // 准备测试数据
        ConsumerRecord<String, String> record1 = new ConsumerRecord<>("test-topic", 0, 0L, "key1", "value1");
        List<ConsumerRecord<String, String>> records = Arrays.asList(record1);

        // 调用带有@SingleTraceForBatch注解的方法
        // 如果AOP正常工作，这个调用应该成功完成
        try {
            batchMessageConsumer.consumeBatchMessagesWithMetadata(records);
            System.out.println("✅ AOP功能测试成功：方法正常执行，没有抛出异常");
        } catch (Exception e) {
            System.err.println("❌ AOP功能测试失败：" + e.getMessage());
            throw e;
        }
    }

    @Test
    void testAopWithEmptyList() {
        // 测试空列表的情况
        List<ConsumerRecord<String, String>> emptyRecords = Arrays.asList();

        try {
            batchMessageConsumer.consumeBatchMessagesWithMetadata(emptyRecords);
            System.out.println("✅ 空列表测试成功：方法正常执行");
        } catch (Exception e) {
            System.err.println("❌ 空列表测试失败：" + e.getMessage());
            throw e;
        }
    }
}
