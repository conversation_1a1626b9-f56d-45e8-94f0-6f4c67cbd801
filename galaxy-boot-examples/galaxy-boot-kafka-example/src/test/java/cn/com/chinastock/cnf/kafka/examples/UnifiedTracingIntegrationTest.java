package cn.com.chinastock.cnf.kafka.examples;

import cn.com.chinastock.cnf.kafka.examples.consumer.BatchMessageConsumer;
import cn.com.chinastock.cnf.kafka.examples.service.KafkaProducerService;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 统一链路追踪集成测试
 *
 * <AUTHOR> Boot Team
 */
@SpringBootTest
@TestPropertySource(properties = {
    "galaxy.kafka.enabled=true",
    "galaxy.kafka.tracing.unified-batch-enabled=true",
    "spring.kafka.bootstrap-servers=localhost:9092",
    "spring.kafka.consumer.group-id=test-group",
    "logging.level.cn.com.chinastock.cnf.kafka.aspect=DEBUG"
})
class UnifiedTracingIntegrationTest {

    @Autowired
    private BatchMessageConsumer batchMessageConsumer;

    @MockBean
    private KafkaProducerService kafkaProducerService;

    @Test
    void testBatchMessageConsumerWithUnifiedTracing() {
        // 准备测试数据
        ConsumerRecord<String, String> record1 = new ConsumerRecord<>("test-topic", 0, 0L, "key1", "value1");
        ConsumerRecord<String, String> record2 = new ConsumerRecord<>("test-topic", 0, 1L, "key2", "value2");
        List<ConsumerRecord<String, String>> records = Arrays.asList(record1, record2);

        // 验证Bean正确注入
        assertNotNull(batchMessageConsumer);

        // 调用批量处理方法 - 这应该触发AOP切面
        // 这个测试主要验证方法能够正常执行而不抛出异常
        batchMessageConsumer.consumeBatchMessagesWithMetadata(records);
    }

    @Test
    void testBatchMessageConsumerWithEmptyRecords() {
        // 测试空记录列表的情况
        List<ConsumerRecord<String, String>> emptyRecords = Arrays.asList();

        // 调用批量处理方法
        batchMessageConsumer.consumeBatchMessagesWithMetadata(emptyRecords);
    }
}
