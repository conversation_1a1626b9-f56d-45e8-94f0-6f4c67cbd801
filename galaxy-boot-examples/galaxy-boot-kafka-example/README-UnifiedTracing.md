# Galaxy Boot Kafka 统一链路追踪演示

## 概述

本示例演示了如何使用 `@SingleTraceForBatch` 注解来解决Kafka批量消息处理中traceId不一致的问题。

## 问题描述

在原始的实现中，批量消息处理过程中发送消息到其他topic时会产生新的traceId，导致链路追踪不连续：

```
# 处理批量消息
V1|2025-09-04T21:18:54.993+0800|...|6fc5a4a46e3b4ba912439abb07f32d69|7fac7cf3bdd04344|d03a4f3e9def4e1b|...|Batch consumer with metadata received 1 records

# 发送消息到其他topic - 使用了新的traceId
V1|2025-09-04T21:18:55.005+0800|...|86cb42c0f62387a587ae47d4bd16e1b8|7fac7cf3bdd04344|cfa3c40d29c5c464|...|Sending message to topic: another-topic
```

## 解决方案

使用 `@SingleTraceForBatch` 注解标记批量处理方法：

```java
@KafkaListener(topics = "batch-message-with-metadata-topic", groupId = "batch-metadata-group")
@SingleTraceForBatch(spanName = "batch-metadata-processing", tags = {"consumer.type=batch-metadata"})
public void consumeBatchMessagesWithMetadata(List<ConsumerRecord<String, String>> records) {
    // 批量处理逻辑
    // 发送消息到其他topic的操作也会在同一个trace下
}
```

## 运行演示

### 1. 启动Kafka

确保Kafka服务运行在 `localhost:9092`

### 2. 启动应用

```bash
cd galaxy-boot-examples/galaxy-boot-kafka-example
mvn spring-boot:run
```

### 3. 发送测试消息

使用HTTP客户端发送批量消息：

```bash
curl -X POST http://localhost:8090/api/kafka/batch/send-batch-metadata \
  -H "Content-Type: application/json" \
  -d '["test-message-1", "test-message-2"]' \
  --data-urlencode "keyPrefix=TEST"
```

### 4. 观察日志

在应用日志中，你会看到统一的traceId：

```
# 处理批量消息 - 使用统一的traceId
V1|2025-09-04T21:18:54.993+0800|...|6fc5a4a46e3b4ba912439abb07f32d69|7fac7cf3bdd04344|d03a4f3e9def4e1b|...|Batch consumer with metadata received 2 records

# 发送消息到其他topic - 使用相同的traceId
V1|2025-09-04T21:18:55.005+0800|...|6fc5a4a46e3b4ba912439abb07f32d69|7fac7cf3bdd04344|cfa3c40d29c5c464|...|Sending message to topic: another-topic
```

## 配置选项

在 `application.yml` 中可以配置统一链路追踪功能：

```yaml
galaxy:
  kafka:
    enabled: true
    tracing:
      unified-batch-enabled: true  # 默认为true，启用统一链路追踪
```

## 注解参数

`@SingleTraceForBatch` 注解支持以下参数：

- `spanName`: Span名称，默认为 "batch-processing"
- `recordBatchSize`: 是否记录批量大小，默认为 true
- `tags`: 自定义标签数组，格式为 "key=value"

## 验证效果

1. **统一的traceId**: 整个批量处理过程使用相同的traceId
2. **Span标签**: 自动记录批量大小和自定义标签
3. **链路完整性**: 所有相关操作都在同一个链路追踪上下文中

## 注意事项

1. 被注解的方法必须包含一个 `List` 类型的参数
2. 需要确保Micrometer Tracing依赖可用
3. 方法必须被Spring管理（通过@Component等注解）
4. 不能是private或final方法（AOP限制）

## 故障排除

如果功能未生效，请检查：

1. 配置是否正确启用
2. Micrometer Tracing依赖是否存在
3. 方法是否被Spring代理
4. 日志级别是否正确设置

## 相关文件

- `BatchMessageConsumer.java`: 演示如何使用注解
- `UnifiedTraceAspect.java`: AOP切面实现
- `SingleTraceForBatch.java`: 注解定义
- `UnifiedTracing.md`: 详细文档
